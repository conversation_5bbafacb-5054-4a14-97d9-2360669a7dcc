<template>
  <div class="manual-review-form">
    <!-- 审核采购文件：使用audit-comp组件 -->
    <div v-if="isAuditDocument">
      <audit-comp
        ref="auditComp"
        :is-dialog="false"
        :isBatch="false"
      />

      <!-- 操作按钮 -->
      <div class="action-btn">
        <el-button
          type="primary"
          @click="handleAuditSave"
          :loading="saveLoading"
          class="save-btn"
        >
          保存
        </el-button>
      </div>
    </div>

    <!-- 确认采购文件：使用原有的审核表单 -->
    <div v-else>
      <el-form
        ref="reviewForm"
        :model="formData"
        :rules="formRules"
        label-width="90px"
        class="review-form"
      >
        <!-- 审核意见字段 -->
        <el-form-item label="审核意见" prop="option">
          <el-input
            v-model="formData.option"
            type="textarea"
            :rows="5"
            placeholder="请输入审核意见"
            maxlength="500"
            show-word-limit
            resize="none"
            class="review-textarea"
          />
        </el-form-item>

        <!-- 审核结果字段 -->
        <el-form-item label="审核结果" prop="confirmResut">
          <el-radio-group v-model="formData.confirmResut" class="review-radio-group">
            <el-radio label="0" class="review-radio">
              <span class="radio-text">审核通过</span>
            </el-radio>
            <el-radio label="1" class="review-radio">
              <span class="radio-text">退回修改</span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="action-btn">
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saveLoading"
          class="save-btn"
        >
          保存
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import AuditComp from '@/components/page/audit-comp.vue'

export default {
  name: 'ManualReviewForm',
  components: {
    AuditComp
  },
  props: {
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({
        option: '',
        confirmResut: ''
      })
    },
    moduleType: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      saveLoading: false,
      formData: {
        option: '',
        confirmResut: ''
      },
      formRules: {
        option: [
          { required: true, message: '请输入审核意见', trigger: 'blur' },
          { max: 500, message: '审核意见不能超过500个字符', trigger: 'blur' }
        ],
        confirmResut: [
          { required: true, message: '请选择审核结果', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    initialData: {
      handler(newVal) {
        if (newVal) {
          this.formData = { ...newVal };
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    // 判断是否为审核文档
    isAuditDocument() {
      return this.moduleType && this.moduleType.includes('审核')
    }
  },
  mounted() {
    if (this.isAuditDocument) {
      this.$nextTick(() => {
        // 初始化额外表单元素组件
        this.$refs.auditComp.selectAuditData([this.rowData.getRowData()])
      })
    }
  },
  methods: {
    // 保存表单
    handleSave() {
      this.$refs.reviewForm.validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          // 构造保存数据
          const saveData = {
            option: this.formData.option.trim(),
            confirmResut: this.formData.confirmResut,
          };
          // 触发保存事件
          this.$emit('save', saveData);
          this.saveLoading = false;
        } else {
          this.$message.warning('请完善表单信息');
          return false;
        }
      });
    },
    // 处理审核文档的保存
    handleAuditSave() {
      if (this.$refs.auditComp && this.$refs.auditComp.submitAudit) {
        this.saveLoading = true;
        try {
          // 调用audit-comp组件的submitAudit方法
          this.$refs.auditComp.submitAudit();
          // 关闭弹窗 刷新列表
          this.$emit('save');
        } catch (error) {
          console.error('审核保存失败:', error);
          this.$message.error('审核保存失败，请重试');
        } finally {
          this.saveLoading = false;
        }
      } else {
        this.$message.error('审核组件未正确加载，请刷新页面重试');
      }
    },
    // 重置表单
    handleReset() {
      this.$refs.reviewForm.resetFields();
      this.formData = {
        option: '',
        confirmResut: ''
      };
      this.$emit('reset');
    },
    // 验证表单
    validateForm() {
      return new Promise((resolve) => {
        this.$refs.reviewForm.validate((valid) => {
          resolve(valid);
        });
      });
    },
    // 获取表单数据
    getFormData() {
      return {
        option: this.formData.option.trim(),
        confirmResut: this.formData.confirmResut
      };
    },
    // 设置表单数据
    setFormData(data) {
      this.formData = { ...data };
    },
    // 清空验证
    clearValidate() {
      this.$refs.reviewForm.clearValidate();
    }
  }
}
</script>

<style scoped lang="scss">
.manual-review-form {
  padding: 24px 16px;
  background-color: #fff;
  min-height: 400px;

  .action-btn {
    margin-top: 16px;
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

.review-form {
  max-width: 600px;
  margin: 0 auto;

  .el-form-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  ::v-deep .el-form-item__label {
    color: #303133;
    font-weight: 500;
    line-height: 1.5;
  }
}

.review-textarea {
  ::v-deep .el-textarea__inner {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    font-size: 14px;
    line-height: 1.5;

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

.review-radio-group {
  display: flex;
  align-items: center;
  gap: 12px;

  .review-radio {
    margin-right: 0;
    margin-bottom: 0;
    line-height: 20px;
    ::v-deep .el-radio__label {
      padding-left: 8px;
    }
  }
  .radio-text {
    font-size: 14px;
    font-weight: 500;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 14px;
    margin: 0;
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .manual-review-form {
    padding: 16px;
  }

  .review-form {
    max-width: 100%;

    ::v-deep .el-form-item__label {
      width: 80px !important;
    }
  }

  .review-radio-group {
    .radio-text {
      font-size: 13px;
    }
  }
}

// 表单验证样式
::v-deep .el-form-item.is-error {
  .el-textarea__inner {
    border-color: #f56c6c;
  }

  .el-radio__input.is-checked .el-radio__inner {
    border-color: #f56c6c;
    background-color: #f56c6c;
  }
}

// 加载状态样式
.save-btn.is-loading {
  pointer-events: none;
}

// 无障碍支持
.review-form {
  ::v-deep .el-radio__input:focus .el-radio__inner {
    outline: 2px solid #409eff;
    outline-offset: 2px;
  }

  ::v-deep .el-textarea__inner:focus {
    outline: none;
  }
}
</style>
