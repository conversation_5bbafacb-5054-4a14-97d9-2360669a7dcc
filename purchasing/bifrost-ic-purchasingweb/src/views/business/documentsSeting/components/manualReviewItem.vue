<template>
  <div class="manual-review-item">
    <!-- 顶部：审核日期 + 状态 -->
    <div class="item-header">
      <div class="reviewer-info">
        <span class="reviewer-name" v-if="item.reviewPerson && smartCheckTabConfig === 'two'">审核人:{{ item.reviewPerson }}</span>
        <span class="review-date" v-if="item.reviewTime">审核日期:{{ item.reviewTime }}</span>
      </div>
      <div class="status-badge" :class="statusClass">
        {{ statusText }}
      </div>
    </div>

    <!-- 中间：内容编辑区域 -->
    <div class="item-content">
      <el-input
        v-model="editableContent"
        type="textarea"
        :rows="4"
        :disabled="!isEditing"
        :class="{ 'readonly-textarea': !isEditing, 'editable-textarea': isEditing }"
        placeholder="请输入审核内容"
        maxlength="500"
        show-word-limit
        resize="none"
      />
    </div>

    <!-- 底部：操作按钮 -->
    <div class="item-actions">
      <el-button
        size="mini"
        type="primary"
        @click="handleLocate"
        class="action-btn"
        :loading="locateLoading"
      >
        定位原文
      </el-button>

      <template v-if="smartCheckTabConfig === 'three'">
        <!-- 默认状态：显示修改按钮 -->
        <el-button
          v-if="!isEditing"
          size="mini"
          type="primary"
          @click="handleEdit"
          class="action-btn"
        >
          修改
        </el-button>

        <!-- 编辑状态：显示保存按钮 -->
        <el-button
          v-if="isEditing"
          size="mini"
          type="primary"
          @click="handleSave"
          class="action-btn"
          :loading="saveLoading"
          :disabled="!isContentChanged"
        >
          保存
        </el-button>

        <el-button
          size="mini"
          @click="handleDelete"
          class="action-btn"
          :loading="deleteLoading"
        >
          删除本条
        </el-button>
      </template>
      <template v-if="smartCheckTabConfig === 'two'">
        <!-- 采纳和不采纳按钮 -->
        <el-button
          size="mini"
          type="primary"
          @click="handleAccept"
          class="action-btn"
          :loading="acceptLoading"
          :disabled="item.productionStatus === '已采纳'"
        >
          采纳
        </el-button>

        <el-button
          size="mini"
          @click="handleReject"
          class="action-btn"
          :loading="rejectLoading"
          :disabled="item.productionStatus === '未采纳'"
        >
          不采纳
        </el-button>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ManualReviewItem',
  props: {
    item: {
      type: Object,
      required: true,
      default: () => ({
        id: '',
        reviewTime: '',
        content: '',
        productionStatus: '未处理', // unprocessed, adopted, not_adopted
        catalog: '',
      })
    }
  },
  data() {
    return {
      isEditing: false,
      editableContent: '',
      originalContent: '',
      locateLoading: false,
      saveLoading: false,
      deleteLoading: false,
      acceptLoading: false,
      rejectLoading: false
    }
  },
  inject: ['smartCheckTabConfig'],
  computed: {
    statusText() {
      return this.item.productionStatus || '未处理'
    },
    statusClass() {
      return {
        'status-unprocessed': this.item.productionStatus === '未处理' || !this.item.productionStatus,
        'status-adopted': this.item.productionStatus === '已采纳',
        'status-not-adopted': this.item.productionStatus === '未采纳'
      };
    },
    isContentChanged() {
      return this.editableContent !== this.originalContent;
    }
  },
  watch: {
    'item.content': {
      handler(newVal) {
        this.editableContent = newVal || '';
        this.originalContent = newVal || '';
      },
      immediate: true
    }
  },
  methods: {
    handleLocate() {
      this.locateLoading = true;
      // 触发定位原文事件，传递树节点ID
      this.$emit('locate', this.item);
      // 模拟定位操作延迟
      setTimeout(() => {
        this.locateLoading = false;
      }, 500);
    },
    handleEdit() {
      this.isEditing = true;
      this.originalContent = this.editableContent;
      // 触发编辑事件
      this.$emit('edit', this.item.id);
    },
    handleSave() {
      if (!this.isContentChanged) {
        this.$message.warning('内容未发生变化');
        return;
      }
      if (!this.editableContent.trim()) {
        this.$message.warning('内容不能为空');
        return;
      }
      this.saveLoading = true;
      // 触发保存事件
      this.$emit('save', {
        id: this.item.id,
        content: this.editableContent.trim(),
        saveSuccess: this.saveSuccess
      });
    },
    saveSuccess() {
      this.saveLoading = false;
      this.isEditing = false;
      this.originalContent = this.editableContent;
    },
    handleDelete() {
      this.$confirm('确定要删除这条审核记录吗？删除后无法恢复。', '确认删除', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteLoading = true;
        const data = {
          id: this.item.id,
          callback: () => {
            this.deleteLoading = false;
          }
        }
        // 触发删除事件
        this.$emit('delete', data);
      }).catch(() => {
        // 用户取消删除，不需要任何操作
      });
    },

    handleAccept() {
      this.acceptLoading = true;

      // 调用 saveCheckResult API
      this.$callApi('saveCheckResult', {
        bizid: this.item.id,
        productionStatus: '已采纳'
      }, (result) => {
        if (result.success) {
          this.$message.success('采纳成功');
          // 触发数据刷新事件
          this.$emit('refresh-data');
        } else {
          this.$message.error(result.message || '采纳失败，请重试');
        }
        this.acceptLoading = false;
        return true;
      }, (error) => {
        this.$message.error('采纳失败，请重试');
        this.acceptLoading = false;
      }, {
        getExParamsCallApiSave(data) {
          return `&page=修改`
        }
      });
    },

    handleReject() {
      this.rejectLoading = true;

      // 调用 saveCheckResult API
      this.$callApi('saveCheckResult', {
        bizid: this.item.id,
        productionStatus: '未采纳'
      }, (result) => {
        if (result.success) {
          this.$message.success('不采纳成功');
          // 触发数据刷新事件
          this.$emit('refresh-data');
        } else {
          this.$message.error(result.message || '不采纳失败，请重试');
        }
        this.rejectLoading = false;
        return true;
      }, (error) => {
        this.$message.error('不采纳失败，请重试');
        this.rejectLoading = false;
      }, {
        getExParamsCallApiSave(data) {
          return `&page=修改`
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">
// 统一与 smart-check-item 的样式
.manual-review-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  background-color: #fff;

  &:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reviewer-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #303133;
  font-size: 12px;
}

// 统一状态徽章样式，与 smart-check-item 保持一致
.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.status-unprocessed {
    background-color: #f4f4f5;
    color: #909399;
  }

  &.status-adopted {
    background-color: #f0f9ff;
    color: #67c23a;
  }

  &.status-not-adopted {
    background-color: #fef0f0;
    color: #f56c6c;
  }
}

.item-content {
  margin-bottom: 12px;

  // 只读状态样式，参考 smart-check-item 的 content-text 样式
  .readonly-textarea {
    ::v-deep .el-textarea__inner {
      background-color: #f8f9fa;
      border: 1px solid #e4e7ed;
      color: #606266;
      cursor: default;
      resize: none;
      border-radius: 4px;
      border-left: 3px solid #409eff;
      font-size: 14px;
      line-height: 1.5;
      padding: 8px 12px;

      &:focus {
        border-color: #e4e7ed;
        border-left: 3px solid #409eff;
        box-shadow: none;
      }
    }
  }

  // 编辑状态样式
  .editable-textarea {
    ::v-deep .el-textarea__inner {
      background-color: #fff;
      border: 1px solid #409eff;
      color: #303133;
      border-radius: 4px;
      font-size: 14px;
      line-height: 1.5;
      padding: 8px 12px;

      &:focus {
        border-color: #66b1ff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }
}

// 统一按钮样式，与 smart-check-item 保持一致
.item-actions {
  display: flex;
  gap: 8px;

  .action-btn {
    font-size: 12px;
    padding: 5px 12px;
    height: 28px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .manual-review-item {
    padding: 12px;
    margin-bottom: 12px;
  }

  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .item-actions {
    flex-wrap: wrap;
    gap: 8px;

    .action-btn {
      flex: 1;
      min-width: 80px;
    }
  }
}
</style>
